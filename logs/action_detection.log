2025-08-04 11:11:24 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:11:24 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:11:24 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:11:24 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:11:24 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:11:24 | INFO     | __main__:main:167 | 📹 视频源: 0
2025-08-04 11:11:24 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:11:24 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:11:24 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:11:24 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:11:24 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:11:24 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:11:24 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:11:24 | INFO     | src.detection.video_capture:_initialize_capture:39 | 初始化摄像头: 0
2025-08-04 11:11:24 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1280x720, FPS: 10.00
2025-08-04 11:11:24 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:11:24 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:11:24 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:11:24 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:11:24 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:11:24 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:11:24 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:11:24 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:11:24 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:11:24 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 1081.7ms, 平均: 1081.7ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.6ms, 平均: 543.6ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.5ms, 平均: 364.3ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.9ms, 平均: 274.7ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.8ms, 平均: 221.3ms
2025-08-04 11:11:26 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:11:39 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:11:39 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:11:39 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 68, 总运行时间: 15.4s, 平均FPS: 4.4, 检测到的动作数: 0
2025-08-04 11:11:39 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:11:39 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:11:39 | INFO     | __main__:main:239 | ============================================================
2025-08-04 11:12:45 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:12:45 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:12:45 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:12:45 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:12:45 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:12:45 | INFO     | __main__:main:167 | 📹 视频源: 0
2025-08-04 11:12:45 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:12:45 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:12:45 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:12:45 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:12:45 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:12:45 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:12:45 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:12:45 | INFO     | src.detection.video_capture:_initialize_capture:39 | 初始化摄像头: 0
2025-08-04 11:12:45 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1280x720, FPS: 10.00
2025-08-04 11:12:45 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:12:45 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:12:45 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:12:45 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:12:45 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:12:45 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:12:45 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:12:45 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:12:45 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:12:45 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 1193.5ms, 平均: 1193.5ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.9ms, 平均: 599.7ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 5.7ms, 平均: 401.7ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.0ms, 平均: 303.0ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=0, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:219 | 推理时间: 9.0ms, 平均: 244.2ms
2025-08-04 11:12:47 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:09 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:13:09 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:13:09 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 107, 总运行时间: 23.7s, 平均FPS: 4.5, 检测到的动作数: 0
2025-08-04 11:13:09 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:13:09 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:13:09 | INFO     | __main__:main:239 | ============================================================
2025-08-04 11:13:12 | INFO     | src.utils.logger:setup_logger:55 | 日志系统初始化完成，级别: INFO
2025-08-04 11:13:12 | INFO     | src.utils.logger:setup_logger:57 | 日志文件: logs/action_detection.log
2025-08-04 11:13:12 | INFO     | __main__:main:148 | ============================================================
2025-08-04 11:13:12 | INFO     | __main__:main:149 | 🚀 YOLOv8实时动作检测系统启动
2025-08-04 11:13:12 | INFO     | __main__:main:150 | ============================================================
2025-08-04 11:13:12 | INFO     | __main__:main:156 | 使用命令行指定的视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:12 | INFO     | __main__:main:167 | 📹 视频源: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:12 | INFO     | __main__:main:168 | 🤖 模型路径: scripts/models/yolov8s.pt
2025-08-04 11:13:12 | INFO     | __main__:main:169 | 📁 输出目录: output/detected_actions
2025-08-04 11:13:12 | INFO     | __main__:main:170 | 🎯 置信度阈值: 0.5
2025-08-04 11:13:12 | INFO     | __main__:main:171 | 📊 IoU阈值 - 持有: 0.3, 容器: 0.5, 释放: 0.2
2025-08-04 11:13:12 | INFO     | __main__:main:172 | ⏰ 冷却时间: 3.0秒
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:55 | torch.cuda.is_available(): True
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:56 | torch.cuda.device_count(): 1
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:62 | 检测到GPU: NVIDIA GeForce RTX 4060 Laptop GPU (7.6GB)
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:66 | 使用GPU设备: cuda:0
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:88 | 使用设备: 0
2025-08-04 11:13:12 | INFO     | src.detection.detector:_get_device:97 | GPU缓存已清理
2025-08-04 11:13:13 | INFO     | src.detection.detector:_get_device:101 | GPU内存分配策略已设置
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:117 | 加载模型: scripts/models/yolov8s.pt
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:123 | 将模型移动到GPU设备: 0
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:129 | 模型已成功移动到GPU
2025-08-04 11:13:13 | INFO     | src.detection.detector:_load_model:138 | 检测类别: {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench', 14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet', 62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'}
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_initialize_capture:52 | 初始化视频文件: data/raw_videos/short_video_for_test.mp4
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_log_video_info:74 | 视频信息 - 分辨率: 1442x898, FPS: 25.02
2025-08-04 11:13:13 | INFO     | src.detection.video_capture:_log_video_info:79 | 视频时长: 60.36秒, 总帧数: 1510
2025-08-04 11:13:13 | INFO     | src.state_machine.action_state_machine:__init__:67 | 状态机初始化完成，初始状态: idle
2025-08-04 11:13:13 | INFO     | src.state_machine.action_detector:__init__:94 | 动作检测器初始化完成
2025-08-04 11:13:13 | INFO     | __main__:main:202 | 🖥️  推理设备: 0
2025-08-04 11:13:13 | INFO     | __main__:main:213 | 💡 控制提示:
2025-08-04 11:13:13 | INFO     | __main__:main:214 |    按 'q' 或 ESC 退出程序
2025-08-04 11:13:13 | INFO     | __main__:main:215 |    按 'r' 重置状态机
2025-08-04 11:13:13 | INFO     | __main__:main:216 |    按 'd' 切换调试模式
2025-08-04 11:13:13 | INFO     | __main__:main:218 | 🔍 开始检测...
2025-08-04 11:13:13 | INFO     | __main__:main:219 | ------------------------------------------------------------
2025-08-04 11:13:13 | INFO     | src.state_machine.action_detector:run:113 | 开始运行动作检测
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #1: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 1016.2ms, 平均: 1016.2ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9184210300445557, 0.5974879264831543]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #2: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.9ms, 平均: 511.6ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9065991640090942, 0.6765909194946289]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #3: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 7.5ms, 平均: 343.5ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9199779033660889, 0.6526250243186951]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #4: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.3ms, 平均: 259.2ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9236124753952026, 0.5702254772186279]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:217 | 检测调试 #5: 原始检测数=2, 过滤后检测数=0, 置信度阈值=0.5
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:219 | 推理时间: 6.6ms, 平均: 208.7ms
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:224 | GPU内存使用: 74.6MB
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:227 | 原始类别: [0.0, 69.0]
2025-08-04 11:13:14 | INFO     | src.detection.detector:detect:228 | 原始置信度: [0.9197899103164673, 0.5257490277290344]
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:348 | 统计信息 - 帧数: 300, 运行时间: 11.1s, FPS: 27.0, 状态: idle, 动作数: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:356 | 检测统计 - 有检测的帧: 0/300 (0.0%), 总检测数: 0, 手: 0, 产品: 0, 容器: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:365 | 性能统计 - 平均推理时间: 16.1ms, 估计FPS: 62.0, 设备: 0
2025-08-04 11:13:24 | INFO     | src.state_machine.action_detector:_log_statistics:370 | GPU内存 - 已分配: 74.6MB, 已缓存: 128.0MB
2025-08-04 11:13:27 | INFO     | src.state_machine.action_detector:run:155 | 检测到键盘中断，正在退出...
2025-08-04 11:13:27 | INFO     | src.detection.video_capture:release:203 | 视频捕获资源已释放
2025-08-04 11:13:27 | INFO     | src.state_machine.action_detector:_cleanup:385 | 检测完成 - 总帧数: 409, 总运行时间: 14.8s, 平均FPS: 27.7, 检测到的动作数: 0
2025-08-04 11:13:27 | INFO     | __main__:main:237 | ============================================================
2025-08-04 11:13:27 | INFO     | __main__:main:238 | 👋 程序结束
2025-08-04 11:13:27 | INFO     | __main__:main:239 | ============================================================

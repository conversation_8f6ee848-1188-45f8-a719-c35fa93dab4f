"""
动作检测器
整合检测模块和状态机，提供高级动作检测接口
"""

import cv2
import time
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from loguru import logger

from .action_state_machine import ActionStateMachine
from ..detection.detector import YOLODetector
from ..detection.video_capture import VideoCapture


class ActionDetector:
    """动作检测器类"""
    
    def __init__(self,
                 model_path: str,
                 video_source: Any,
                 conf_threshold: float = 0.5,
                 iou_held_threshold: float = 0.3,
                 iou_in_container_threshold: float = 0.5,
                 iou_release_threshold: float = 0.2,
                 cooldown_seconds: float = 3.0,
                 fps_limit: Optional[int] = None,
                 save_images: bool = True,
                 image_save_path: str = "output/detected_actions",
                 device: str = 'auto'):
        """
        初始化动作检测器
        
        Args:
            model_path: YOLO模型路径
            video_source: 视频源
            conf_threshold: 检测置信度阈值
            iou_held_threshold: 手持有产品的IoU阈值
            iou_in_container_threshold: 产品进入容器的IoU阈值
            iou_release_threshold: 手与产品分离的IoU阈值
            cooldown_seconds: 动作触发后的冷却时间
            fps_limit: 帧率限制
            save_images: 是否保存检测到动作时的图像
            image_save_path: 图像保存路径
            device: 推理设备 ('cpu', 'cuda', 'auto')
        """
        self.save_images = save_images
        self.image_save_path = Path(image_save_path)
        
        # 创建保存目录
        if self.save_images:
            self.image_save_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化检测器
        self.detector = YOLODetector(
            model_path=model_path,
            conf_threshold=conf_threshold,
            device=device
        )
        
        # 初始化视频捕获
        self.video_capture = VideoCapture(
            source=video_source,
            fps_limit=fps_limit
        )
        
        # 初始化状态机
        self.state_machine = ActionStateMachine(
            iou_held_threshold=iou_held_threshold,
            iou_in_container_threshold=iou_in_container_threshold,
            iou_release_threshold=iou_release_threshold,
            cooldown_seconds=cooldown_seconds
        )
        
        # 回调函数
        self.action_callback: Optional[Callable] = None

        # 统计信息
        self.frame_count = 0
        self.start_time = None

        # 调试模式
        self.debug_mode = False
        self.detection_stats = {
            'total_detections': 0,
            'frames_with_detections': 0,
            'hand_detections': 0,
            'product_detections': 0,
            'container_detections': 0
        }

        logger.info("动作检测器初始化完成")
    
    def set_action_callback(self, callback: Callable[[Dict[str, Any], np.ndarray], None]) -> None:
        """
        设置动作检测回调函数
        
        Args:
            callback: 回调函数，参数为(action_info, frame)
        """
        self.action_callback = callback
    
    def run(self, show_video: bool = True, window_name: str = "Action Detection") -> None:
        """
        运行动作检测
        
        Args:
            show_video: 是否显示视频窗口
            window_name: 窗口名称
        """
        logger.info("开始运行动作检测")
        self.start_time = time.time()
        
        try:
            for frame in self.video_capture.get_frame_generator():
                self.frame_count += 1

                # 执行检测
                detections = self.detector.detect(frame)

                # 添加调试输出 - 显示检测结果
                self._log_detection_results(detections)

                # 更新状态机
                action_info = self.state_machine.update(detections)

                # 如果检测到动作
                if action_info is not None:
                    self._handle_action_detected(action_info, frame, detections)

                # 绘制检测结果
                if show_video:
                    display_frame = self._create_display_frame(frame, detections)
                    cv2.imshow(window_name, display_frame)

                    # 检查退出键
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q') or key == 27:  # 'q' 或 ESC
                        logger.info("用户请求退出")
                        break
                    elif key == ord('r'):  # 'r' 重置状态机
                        self.state_machine.reset()
                        logger.info("状态机已重置")
                    elif key == ord('d'):  # 'd' 切换调试模式
                        self.debug_mode = not getattr(self, 'debug_mode', False)
                        logger.info(f"调试模式: {'开启' if self.debug_mode else '关闭'}")

                # 定期输出统计信息
                if self.frame_count % 300 == 0:  # 每300帧输出一次
                    self._log_statistics()
        
        except KeyboardInterrupt:
            logger.info("检测到键盘中断，正在退出...")
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            self._cleanup(show_video)
    
    def _handle_action_detected(self, action_info: Dict[str, Any], 
                               frame: np.ndarray, 
                               detections: Dict[str, List[Dict[str, Any]]]) -> None:
        """处理检测到的动作"""
        logger.success(f"检测到动作: {action_info['action_id']}")
        
        # 保存图像
        if self.save_images:
            self._save_action_image(action_info, frame, detections)
        
        # 调用回调函数
        if self.action_callback is not None:
            try:
                self.action_callback(action_info, frame)
            except Exception as e:
                logger.error(f"回调函数执行出错: {e}")
    
    def _save_action_image(self, action_info: Dict[str, Any], 
                          frame: np.ndarray,
                          detections: Dict[str, List[Dict[str, Any]]]) -> None:
        """保存动作检测图像"""
        try:
            # 绘制检测结果
            result_frame = self.detector.draw_detections(frame, detections)
            
            # 添加状态信息
            result_frame = self._add_info_overlay(result_frame, action_info)
            
            # 生成文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime(action_info['timestamp']))
            filename = f"action_{action_info['action_id']:04d}_{timestamp}.jpg"
            filepath = self.image_save_path / filename
            
            # 保存图像
            cv2.imwrite(str(filepath), result_frame)
            logger.info(f"动作图像已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"保存动作图像失败: {e}")
    
    def _create_display_frame(self, frame: np.ndarray,
                             detections: Dict[str, List[Dict[str, Any]]]) -> np.ndarray:
        """创建显示帧"""
        # 绘制检测结果
        display_frame = self.detector.draw_detections(frame, detections, show_conf=True)

        # 添加状态信息
        display_frame = self._add_state_overlay(display_frame)

        return display_frame

    def _add_state_overlay(self, frame: np.ndarray) -> np.ndarray:
        """添加状态信息覆盖层"""
        overlay_frame = frame.copy()
        state_info = self.state_machine.get_state_info()

        # 设置文本参数
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        color = (255, 255, 255)  # 白色
        bg_color = (0, 0, 0)     # 黑色背景

        # 准备显示文本
        texts = [
            f"State: {state_info['current_state']}",
            f"Actions: {state_info['action_count']}",
            f"Frame: {self.frame_count}",
        ]

        if state_info.get('tracked_object'):
            texts.append(f"Tracking: {state_info['tracked_object']['tracking_duration']:.1f}s")

        if state_info['cooldown_remaining'] > 0:
            texts.append(f"Cooldown: {state_info['cooldown_remaining']:.1f}s")

        # 计算文本区域大小
        text_height = 25
        max_width = 0
        for text in texts:
            (text_width, _), _ = cv2.getTextSize(text, font, font_scale, thickness)
            max_width = max(max_width, text_width)

        # 绘制背景矩形
        bg_height = len(texts) * text_height + 10
        cv2.rectangle(overlay_frame, (10, 10), (max_width + 20, bg_height), bg_color, -1)

        # 绘制文本
        for i, text in enumerate(texts):
            y = 30 + i * text_height
            cv2.putText(overlay_frame, text, (15, y), font, font_scale, color, thickness)

        return overlay_frame

    def _add_info_overlay(self, frame: np.ndarray, action_info: Dict[str, Any]) -> np.ndarray:
        """添加动作信息覆盖层"""
        overlay_frame = frame.copy()

        # 设置文本参数
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        thickness = 2
        color = (0, 255, 0)      # 绿色
        bg_color = (0, 0, 0)     # 黑色背景

        # 准备显示文本
        timestamp_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(action_info['timestamp']))
        texts = [
            f"ACTION DETECTED #{action_info['action_id']}",
            f"Time: {timestamp_str}",
            f"Duration: {action_info['tracked_object']['tracking_duration']:.2f}s",
            f"Confidence: {action_info['tracked_object']['confidence']:.3f}"
        ]

        # 计算文本区域大小
        text_height = 30
        max_width = 0
        for text in texts:
            (text_width, _), _ = cv2.getTextSize(text, font, font_scale, thickness)
            max_width = max(max_width, text_width)

        # 在图像中央显示
        frame_height, frame_width = frame.shape[:2]
        bg_width = max_width + 40
        bg_height = len(texts) * text_height + 20

        start_x = (frame_width - bg_width) // 2
        start_y = (frame_height - bg_height) // 2

        # 绘制背景矩形
        cv2.rectangle(overlay_frame,
                     (start_x, start_y),
                     (start_x + bg_width, start_y + bg_height),
                     bg_color, -1)

        # 绘制边框
        cv2.rectangle(overlay_frame,
                     (start_x, start_y),
                     (start_x + bg_width, start_y + bg_height),
                     color, 3)

        # 绘制文本
        for i, text in enumerate(texts):
            y = start_y + 30 + i * text_height
            cv2.putText(overlay_frame, text, (start_x + 20, y), font, font_scale, color, thickness)

        return overlay_frame

    def _log_detection_results(self, detections: Dict[str, List[Dict[str, Any]]]) -> None:
        """记录检测结果的调试信息"""
        # 统计检测结果
        total_objects = sum(len(objects) for objects in detections.values())

        if total_objects > 0:
            self.detection_stats['frames_with_detections'] += 1
            self.detection_stats['total_detections'] += total_objects

            # 按类别统计
            self.detection_stats['hand_detections'] += len(detections.get('hand', []))
            self.detection_stats['product_detections'] += len(detections.get('product', []))
            self.detection_stats['container_detections'] += len(detections.get('target_container', []))

            # 如果启用调试模式或者是前几帧，输出详细信息
            if self.debug_mode or self.frame_count <= 10 or self.frame_count % 100 == 0:
                logger.info(f"帧 {self.frame_count}: 检测到 {total_objects} 个对象")
                for class_name, objects in detections.items():
                    if objects:
                        logger.info(f"  - {class_name}: {len(objects)} 个")
                        for i, obj in enumerate(objects):
                            bbox = obj['bbox']
                            conf = obj['conf']
                            logger.info(f"    [{i+1}] 置信度: {conf:.3f}, 位置: [{bbox[0]:.0f}, {bbox[1]:.0f}, {bbox[2]:.0f}, {bbox[3]:.0f}]")
        else:
            # 偶尔输出"无检测"信息
            if self.frame_count <= 5 or (self.debug_mode and self.frame_count % 50 == 0):
                logger.debug(f"帧 {self.frame_count}: 未检测到任何对象")

    def _log_statistics(self) -> None:
        """输出统计信息"""
        if self.start_time is None:
            return

        runtime = time.time() - self.start_time
        fps = self.frame_count / runtime if runtime > 0 else 0

        state_info = self.state_machine.get_state_info()

        logger.info(f"统计信息 - 帧数: {self.frame_count}, "
                   f"运行时间: {runtime:.1f}s, "
                   f"FPS: {fps:.1f}, "
                   f"状态: {state_info['current_state']}, "
                   f"动作数: {state_info['action_count']}")

        # 输出检测统计信息
        detection_rate = (self.detection_stats['frames_with_detections'] / self.frame_count * 100) if self.frame_count > 0 else 0
        logger.info(f"检测统计 - 有检测的帧: {self.detection_stats['frames_with_detections']}/{self.frame_count} ({detection_rate:.1f}%), "
                   f"总检测数: {self.detection_stats['total_detections']}, "
                   f"手: {self.detection_stats['hand_detections']}, "
                   f"产品: {self.detection_stats['product_detections']}, "
                   f"容器: {self.detection_stats['container_detections']}")

        # 输出性能统计信息
        perf_stats = self.detector.get_performance_stats()
        if perf_stats['total_inferences'] > 0:
            logger.info(f"性能统计 - 平均推理时间: {perf_stats['avg_inference_time_ms']:.1f}ms, "
                       f"估计FPS: {perf_stats['fps_estimate']:.1f}, "
                       f"设备: {perf_stats['device']}")

            if perf_stats['device'] != 'cpu' and 'gpu_memory_allocated_mb' in perf_stats:
                logger.info(f"GPU内存 - 已分配: {perf_stats['gpu_memory_allocated_mb']:.1f}MB, "
                           f"已缓存: {perf_stats.get('gpu_memory_cached_mb', 0):.1f}MB")

    def _cleanup(self, show_video: bool) -> None:
        """清理资源"""
        if show_video:
            cv2.destroyAllWindows()

        self.video_capture.release()

        # 输出最终统计信息
        if self.start_time is not None:
            runtime = time.time() - self.start_time
            fps = self.frame_count / runtime if runtime > 0 else 0

            logger.info(f"检测完成 - 总帧数: {self.frame_count}, "
                       f"总运行时间: {runtime:.1f}s, "
                       f"平均FPS: {fps:.1f}, "
                       f"检测到的动作数: {self.state_machine.action_count}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        runtime = time.time() - self.start_time if self.start_time else 0
        fps = self.frame_count / runtime if runtime > 0 else 0

        stats = {
            'frame_count': self.frame_count,
            'runtime': runtime,
            'fps': fps,
            'action_count': self.state_machine.action_count,
            'state_machine_info': self.state_machine.get_state_info(),
            'video_info': self.video_capture.get_video_info(),
            'detector_info': self.detector.get_model_info()
        }

        return stats

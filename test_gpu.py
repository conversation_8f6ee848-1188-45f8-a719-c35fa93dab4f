#!/usr/bin/env python3
"""
GPU测试脚本 - 检查GPU配置和性能
"""

import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_pytorch_gpu():
    """测试PyTorch GPU配置"""
    print("🔍 测试PyTorch GPU配置...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"🔧 CUDA版本: {torch.version.cuda}")
        print(f"🎮 CUDA可用: {torch.cuda.is_available()}")
        print(f"🖥️  GPU数量: {torch.cuda.device_count()}")
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"📱 GPU {i}: {props.name}")
                print(f"   💾 内存: {props.total_memory / 1024**3:.1f}GB")
                print(f"   🔢 计算能力: {props.major}.{props.minor}")
                
                # 测试GPU内存分配
                try:
                    torch.cuda.set_device(i)
                    test_tensor = torch.randn(1000, 1000).cuda()
                    print(f"   ✅ 内存分配测试通过")
                    del test_tensor
                    torch.cuda.empty_cache()
                except Exception as e:
                    print(f"   ❌ 内存分配测试失败: {e}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
            
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    return torch.cuda.is_available()

def test_yolo_gpu():
    """测试YOLO GPU性能"""
    print("\n🔍 测试YOLO GPU性能...")
    
    try:
        from ultralytics import YOLO
        import torch
        import cv2
        import numpy as np
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 测试CPU性能
        print("📊 测试CPU性能...")
        model_cpu = YOLO('yolov8n.pt')
        
        start_time = time.time()
        for i in range(10):
            results = model_cpu(test_image, device='cpu', verbose=False)
        cpu_time = (time.time() - start_time) / 10
        print(f"   CPU平均推理时间: {cpu_time*1000:.1f}ms")
        
        # 测试GPU性能（如果可用）
        if torch.cuda.is_available():
            print("🎮 测试GPU性能...")
            model_gpu = YOLO('yolov8n.pt')
            
            # 预热GPU
            for i in range(3):
                results = model_gpu(test_image, device='0', verbose=False)
            torch.cuda.synchronize()
            
            start_time = time.time()
            for i in range(10):
                results = model_gpu(test_image, device='0', verbose=False)
            torch.cuda.synchronize()
            gpu_time = (time.time() - start_time) / 10
            
            print(f"   GPU平均推理时间: {gpu_time*1000:.1f}ms")
            print(f"   🚀 加速比: {cpu_time/gpu_time:.1f}x")
            
            # GPU内存使用情况
            memory_allocated = torch.cuda.memory_allocated() / 1024**2
            memory_reserved = torch.cuda.memory_reserved() / 1024**2
            print(f"   💾 GPU内存使用: {memory_allocated:.1f}MB (已分配), {memory_reserved:.1f}MB (已保留)")
        
    except Exception as e:
        print(f"❌ YOLO GPU测试失败: {e}")
        return False
    
    return True

def test_custom_detector():
    """测试自定义检测器的GPU配置"""
    print("\n🔍 测试自定义检测器GPU配置...")
    
    try:
        from src.detection.detector import YOLODetector
        from src.config import ConfigManager
        
        config = ConfigManager("config.yaml")
        
        # 测试不同设备配置
        devices_to_test = ['auto', 'cpu']
        
        # 如果有GPU，也测试GPU
        import torch
        if torch.cuda.is_available():
            devices_to_test.extend(['cuda', '0'])
        
        for device in devices_to_test:
            print(f"\n📱 测试设备: {device}")
            try:
                detector = YOLODetector(
                    model_path=config.model_path,
                    conf_threshold=0.25,
                    device=device
                )
                
                # 创建测试图像
                test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
                
                # 执行几次推理测试
                start_time = time.time()
                for i in range(5):
                    detections = detector.detect(test_image)
                test_time = (time.time() - start_time) / 5
                
                # 获取性能统计
                perf_stats = detector.get_performance_stats()
                
                print(f"   ✅ 设备配置成功: {perf_stats['device']}")
                print(f"   ⏱️  平均推理时间: {test_time*1000:.1f}ms")
                print(f"   🎯 估计FPS: {1/test_time:.1f}")
                
                if perf_stats['device'] != 'cpu' and 'gpu_name' in perf_stats:
                    print(f"   🎮 GPU: {perf_stats['gpu_name']}")
                    print(f"   💾 GPU内存: {perf_stats.get('gpu_memory_allocated_mb', 0):.1f}MB")
                
            except Exception as e:
                print(f"   ❌ 设备 {device} 配置失败: {e}")
        
    except Exception as e:
        print(f"❌ 自定义检测器测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 GPU配置和性能测试")
    print("=" * 60)
    
    # 测试PyTorch GPU
    gpu_available = test_pytorch_gpu()
    
    # 测试YOLO GPU性能
    test_yolo_gpu()
    
    # 测试自定义检测器
    test_custom_detector()
    
    print("\n" + "=" * 60)
    if gpu_available:
        print("✅ GPU配置正常，建议使用GPU加速")
        print("💡 运行命令: python main.py --debug")
    else:
        print("⚠️  GPU不可用，将使用CPU")
        print("💡 运行命令: python main.py --debug --conf-threshold 0.25")
    
    print("✅ 测试完成！")
